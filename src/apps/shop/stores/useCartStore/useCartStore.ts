import { enableMapSet } from 'immer';
import { apiErrorNotification, createStore } from '@/utils';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { CartType } from '@/libs/cart/types';
import { fetchApi } from '@/libs/utils/api';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { ApiErrorProps } from '@/types/utility';
import type { CheckoutResponseType } from './type';

enableMapSet();

type State = CartType & {
  isCartLoading: boolean;
  errorOnCartLoading: boolean;
  updatingProductIds: Set<string>;
};
type Actions = {
  fetchCart: VoidFunction;
  clearCart: VoidFunction;
  setProductUpdating: (productOfferIds: string[], isUpdating: boolean) => void;
  swapOfferCartItem: (itemId: string, productOfferId: string) => void;
  checkout: () => Promise<CheckoutResponseType>;
  addToCart: (params: {
    offers: {
      productOfferId: string;
      quantity: number;
    }[];
    onError: (message: string) => void;
  }) => void;
};

export const INITIAL_STATE: State = {
  budget: null,
  vendors: [],
  isCartLoading: false,
  errorOnCartLoading: false,
  itemsCount: 0,
  uniqueItemsCount: 0,
  subtotal: '0',
  total: '0',
  updatingProductIds: new Set<string>(),
};

export const useCartStore = createStore<Actions & State>()(
  immer(
    devtools((set, getState) => ({
      ...INITIAL_STATE,
      fetchCart: async () => {
        set({ isCartLoading: true, errorOnCartLoading: false });

        try {
          const response = await fetchApi<CartType>('/cart');

          set(response);
        } catch (error) {
          // TODO: Handle it better
          set({ errorOnCartLoading: true });
          console.error(error);
        }

        set({ isCartLoading: false });
      },
      clearCart: async () => {
        set({ isCartLoading: false });

        const response = await fetchApi<CartType>('/cart', {
          method: 'DELETE',
        });

        set(response);
      },
      setProductUpdating: (productOfferIds, isUpdating) => {
        const { updatingProductIds } = getState();
        const newUpdatingProductIds = new Set(updatingProductIds);

        productOfferIds.forEach((productOfferId) => {
          if (isUpdating) {
            newUpdatingProductIds.add(productOfferId);
          } else {
            newUpdatingProductIds.delete(productOfferId);
          }
        });

        set({
          isCartLoading: isUpdating,
          updatingProductIds: newUpdatingProductIds,
        });
      },
      addToCart: async ({ offers, onError }) => {
        const state = getState();

        // Filter offers that actually need updating
        const offersToUpdate = offers.filter((offer) => {
          const currentItem = state.vendors
            .flatMap((vendor) => vendor.items)
            .find((item) => item.productOfferId === offer.productOfferId);

          return !currentItem || currentItem.quantity !== offer.quantity;
        });

        if (offersToUpdate.length === 0) {
          return;
        }

        const { items, productOfferIds } = offersToUpdate.reduce(
          (acc, { productOfferId, quantity }) => {
            acc.items.push({ productOfferId, quantity, notes: '' });
            acc.productOfferIds.push(productOfferId);
            return acc;
          },
          {
            items: [] as {
              productOfferId: string;
              quantity: number;
              notes: string;
            }[],
            productOfferIds: [] as string[],
          },
        );
        state.setProductUpdating(productOfferIds, true);

        try {
          const response = await fetchApi<{
            subtotal: string;
            itemsCount: number;
            uniqueItemsCount: number;
          }>('/cart/cart-items', {
            method: 'POST',
            body: { items },
          });

          // Update only the lightweight fields, then fetch the full cart
          set((state) => {
            state.subtotal = response.subtotal;
            state.itemsCount = response.itemsCount;
            state.uniqueItemsCount = response.uniqueItemsCount;
          });

          // Fetch the full cart to get all details including vendors, promotions, etc.
          getState().fetchCart();
        } catch (err) {
          const { data } = err as ApiErrorProps;
          apiErrorNotification(data.message);
          onError(data?.message ?? '');
        }

        // Clear updating state for all products
        getState().setProductUpdating(productOfferIds, false);
      },
      checkout: async () => {
        const clinic = useClinicStore.getState().clinic;

        const data = await fetchApi<CheckoutResponseType>(
          `/clinics/${clinic?.id}/orders`,
          {
            method: 'POST',
            body: {
              isBillingSameAsShippingAddress: true,
              paymentMethod: 'INVOICE',
              shippingAddress: clinic?.shippingAddress,
              billingAddress: clinic?.billingAddress,
            },
          },
        );

        return data;
      },
      swapOfferCartItem: async (itemId, productOfferId) => {
        try {
          const response = await fetchApi<CartType>(
            `/cart/cart-items/${itemId}`,
            {
              method: 'PATCH',
              body: {
                productOfferId,
              },
            },
          );

          set(response);
        } catch (err) {
          const { data } = err as ApiErrorProps;

          apiErrorNotification(data.message);
        }
      },
    })),
  ),
);
